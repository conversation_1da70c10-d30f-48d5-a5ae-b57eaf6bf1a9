import { useEffect, useState } from "react";
import MealItem from "./MealItem";

export default function Meals(){
    const [loadedMeals, setLoadedMeals]=useState([])
    useEffect(()=>{
        async function fetchMeals() {
try {
    const response=await fetch('http://localhost:3000/meals');
    if(!response.ok){
        throw new Error('Something went wrong!');
    }
    const data=await response.json();
    setLoadedMeals(data);
    
} catch (error) {
    
}
    }
    fetchMeals();
    },[])
    return <ul id="meals">{loadedMeals.map((meal)=>(
        <MealItem key={meal.id} meal={meal}/>
    ))}</ul>
}